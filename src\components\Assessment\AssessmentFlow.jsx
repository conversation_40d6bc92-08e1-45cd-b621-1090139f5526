import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AssessmentForm from './AssessmentForm';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';
import apiService from '../../services/apiService';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import { transformAssessmentScores, validateAssessmentData } from '../../utils/assessmentTransformers';

/**
 * AssessmentFlow Component
 * 
 * Orchestrates the complete assessment process:
 * 1. VIA Character Strengths (Step 1)
 * 2. RIASEC Holland Codes (Step 2) 
 * 3. Big Five Personality (Step 3)
 * 4. Final submission to API
 */
const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentScores, setAssessmentScores] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false); // Flag to prevent double submission
  const [isProcessingSubmit, setIsProcessingSubmit] = useState(false); // Flag to prevent rapid clicks
  const [error, setError] = useState('');
  const [isDebugMode] = useState(import.meta.env.DEV && false); // Set to true for debug mode
  const [allAssessmentsFilled, setAllAssessmentsFilled] = useState(false); // Flag to indicate all assessments are filled

  // Assessment configurations
  const assessments = [
    {
      step: 1,
      key: 'via',
      data: viaQuestions,
      title: 'VIA Character Strengths'
    },
    {
      step: 2,
      key: 'riasec', 
      data: riasecQuestions,
      title: 'RIASEC Holland Codes'
    },
    {
      step: 3,
      key: 'bigFive',
      data: bigFiveQuestions,
      title: 'Big Five Personality'
    }
  ];

  const currentAssessment = assessments.find(a => a.step === currentStep);
  const totalSteps = assessments.length;

  /**
   * Transform assessment scores to API format using utility functions
   */
  const transformScoresToApiFormat = () => {
    try {
      const apiData = transformAssessmentScores(assessmentScores);

      // Validate the transformed data
      const validation = validateAssessmentData(apiData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      return apiData;
    } catch (error) {
      console.error('🔍 DEBUG - Score transformation error:', error);
      throw error;
    }
  };

  /**
   * Handle assessment completion for current step
   */
  const handleAssessmentSubmit = (scores) => {
    // Prevent rapid successive submissions
    if (isProcessingSubmit) {
      return;
    }

    // Set processing flag to prevent rapid clicks
    setIsProcessingSubmit(true);

    // Store scores for current assessment
    setAssessmentScores(prev => ({
      ...prev,
      [currentAssessment.key]: scores
    }));

    // Reset processing flag after a short delay to allow for state updates
    setTimeout(() => {
      setIsProcessingSubmit(false);
    }, 1000);
  };

  /**
   * Move to next assessment
   */
  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  /**
   * Move to previous assessment
   */
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  /**
   * Fill all assessments with random answers and jump to final phase
   */
  const fillAllAssessments = () => {

    // Function to generate random answers for an assessment
    const generateRandomAnswersForAssessment = (assessmentData) => {
      const answers = {};

      Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
        // Regular questions
        category.questions.forEach((_, index) => {
          const questionKey = `${categoryKey}_${index}`;
          answers[questionKey] = Math.floor(Math.random() * 3) + 5; // Random 5-7 for good scores
        });

        // Reverse questions (for Big Five)
        if (category.reverseQuestions) {
          category.reverseQuestions.forEach((_, index) => {
            const questionKey = `${categoryKey}_reverse_${index}`;
            answers[questionKey] = Math.floor(Math.random() * 3) + 5; // Random 5-7
          });
        }
      });

      return answers;
    };

    // Function to calculate scores from answers
    const calculateScoresFromAnswers = (answers, assessmentData) => {
      const scores = {};

      Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
        let totalScore = 0;
        let questionCount = 0;

        // Regular questions
        category.questions.forEach((_, index) => {
          const questionKey = `${categoryKey}_${index}`;
          if (answers[questionKey]) {
            totalScore += answers[questionKey];
            questionCount++;
          }
        });

        // Reverse questions (for Big Five)
        if (category.reverseQuestions) {
          category.reverseQuestions.forEach((_, index) => {
            const questionKey = `${categoryKey}_reverse_${index}`;
            if (answers[questionKey]) {
              // Reverse the score (8 - original score for 1-7 scale)
              totalScore += (8 - answers[questionKey]);
              questionCount++;
            }
          });
        }

        // Calculate average score (0-100 scale)
        if (questionCount > 0) {
          scores[categoryKey] = Math.round(((totalScore / questionCount) - 1) * (100 / 6)); // Convert 1-7 to 0-100
        }
      });

      return scores;
    };

    // Generate scores for all assessments
    const allScores = {};

    assessments.forEach(assessment => {
      const randomAnswers = generateRandomAnswersForAssessment(assessment.data);
      const scores = calculateScoresFromAnswers(randomAnswers, assessment.data);
      allScores[assessment.key] = scores;


    });

    // Update assessment scores state
    setAssessmentScores(allScores);

    // Set flag to indicate all assessments are filled
    setAllAssessmentsFilled(true);

    // Jump to the last assessment (Big Five - step 3)
    setCurrentStep(3);


  };

  /**
   * Submit all assessments to API
   */
  const submitToApi = async () => {
    setIsSubmitting(true);
    setError('');

    try {
      const apiData = transformScoresToApiFormat();
      const response = await apiService.submitAssessment(apiData);

      if (response.success && response.data?.jobId) {
        // Navigate to status page with job ID
        navigate(`/assessment/status/${response.data.jobId}`, {
          state: { fromSubmission: true }
        });
      } else {
        throw new Error(response.message || 'Failed to submit assessment');
      }
    } catch (err) {
      console.error('🔍 DEBUG - API submission error:', err);
      setError(err.response?.data?.message || err.message || 'Failed to submit assessment');
      setHasSubmitted(false); // Reset flag on error to allow retry
      setIsProcessingSubmit(false); // Reset processing flag on error
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-submit when all assessments are complete
  useEffect(() => {
    const { via, riasec, bigFive } = assessmentScores;

    if (via && riasec && bigFive && !isSubmitting && !hasSubmitted && !isProcessingSubmit) {
      setHasSubmitted(true); // Set flag to prevent double submission
      submitToApi();
    }
  }, [assessmentScores, isSubmitting, hasSubmitted, isProcessingSubmit]);

  // Show loading screen during submission
  if (isSubmitting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner text="Submitting your assessment..." />
          <p className="mt-4 text-gray-600">Please wait while we process your responses</p>
        </div>
      </div>
    );
  }

  // Show error screen if submission failed
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorMessage
            title="Submission Failed"
            message={error}
            onRetry={() => {
              setError('');
              setHasSubmitted(false); // Reset flag to allow retry
              setIsProcessingSubmit(false); // Reset processing flag to allow retry
              submitToApi();
            }}
            retryText="Try Again"
          />
        </div>
      </div>
    );
  }

  // Render current assessment
  return (
    <AssessmentForm
      assessmentData={currentAssessment.data}
      onSubmit={handleAssessmentSubmit}
      onNext={handleNext}
      onPrevious={handlePrevious}
      isLastAssessment={currentStep === totalSteps}
      currentStep={currentStep}
      totalSteps={totalSteps}
      isDebugMode={isDebugMode}
      isProcessingSubmit={isProcessingSubmit}
      onFillAllAssessments={fillAllAssessments}
      allAssessmentsFilled={allAssessmentsFilled}
    />
  );
};

export default AssessmentFlow;
